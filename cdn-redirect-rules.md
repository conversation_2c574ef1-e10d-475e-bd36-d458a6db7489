# CDN重定向规则配置

如果您想保持History模式而不是Hash模式，需要在CDN配置中添加以下重定向规则：

## 腾讯云COS配置

在COS控制台的"静态网站"设置中：

1. **重定向规则**：
```json
[
  {
    "Condition": {
      "HttpErrorCodeReturnedEquals": "404"
    },
    "Redirect": {
      "ReplaceKeyWith": "index.html"
    }
  }
]
```

2. **错误文档**：
   - 错误文档：`index.html`

## 阿里云OSS配置

在OSS控制台的"静态页面"设置中：

1. **错误页面**：
   - 404错误页面：`index.html`

2. **重定向规则**：
```xml
<RoutingRules>
  <RoutingRule>
    <RuleNumber>1</RuleNumber>
    <Condition>
      <HttpErrorCodeReturnedEquals>404</HttpErrorCodeReturnedEquals>
    </Condition>
    <Redirect>
      <ReplaceKeyWith>index.html</ReplaceKeyWith>
    </Redirect>
  </RoutingRule>
</RoutingRules>
```

## AWS S3配置

在S3控制台的"静态网站托管"设置中：

1. **错误文档**：`index.html`

2. **重定向规则**：
```json
[
  {
    "Condition": {
      "HttpErrorCodeReturnedEquals": "404"
    },
    "Redirect": {
      "ReplaceKeyWith": "index.html"
    }
  }
]
```

## Nginx配置（如果使用自建CDN）

```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

## 注意事项

1. 配置重定向规则后，所有404错误都会返回index.html
2. 确保您的Vue应用能正确处理直接访问的路由
3. 可能需要等待CDN缓存刷新才能生效
