import { h, markRaw } from "vue";
import { NIcon, NTag, NInput, NSelect, NButton } from "naive-ui";
import {
  CreateOutline,
  TrashOutline,
  CopyOutline,
  CheckmarkOutline,
  CloseOutline,
} from "@vicons/ionicons5";
import { getRoleNameById } from "@/utils/roleCache";

// 使用 markRaw 包装图标组件
const CreateOutlineIcon = markRaw(CreateOutline);
const TrashOutlineIcon = markRaw(TrashOutline);
const CopyOutlineIcon = markRaw(CopyOutline);
const CheckmarkOutlineIcon = markRaw(CheckmarkOutline);
const CloseOutlineIcon = markRaw(CloseOutline);

export function createColumns({
  editingRowId,
  businessRoleOptions,
  copyToClipboard,
  handleSaveEdit,
  handleCancelEdit,
  handleEditRow,
  handleDeleteMember,
  openDataRangeSelector,
  removeSelectedOrg,
  clearRowDataRange,
}) {
  return [
    {
      type: "selection",
      width: 50,
    },
    {
      title: "员工ID",
      key: "agentId",
      width: 140,
      render(row) {
        return h(
          "div",
          {
            style: {
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
            },
            onClick: () => copyToClipboard(row.agentId),
            title: "点击复制员工ID",
          },
          [
            h(
              NIcon,
              {
                size: 16,
                color: "var(--primary-color)",
                style: { marginRight: "5px" },
              },
              { default: () => h(CopyOutlineIcon) }
            ),
            h("span", { style: { fontWeight: 600 } }, row.agentId),
          ]
        );
      },
    },
    {
      title: "姓名",
      width: 240,
      key: "agentName",
      render(row) {
        return h(
          "span",
          {
            style: {
              fontWeight: 600,
            },
          },
          row.agentName
        );
      },
    },
    {
      title: "业务角色",
      key: "businessRole",
      width: 240,
      render(row) {
        const isEditing = editingRowId.value === row.id;

        if (isEditing) {
          return h(NSelect, {
            value: row.businessRole,
            size: "small",
            options: businessRoleOptions.value,
            placeholder: "请选择业务角色",
            filterable: true,
            clearable: true,
            onUpdateValue: (value) => {
              row.businessRole = value;
            },
          });
        }

        // 优先从businessRoleOptions中查找角色名称，如果没有则从缓存中获取
        let roleLabel = businessRoleOptions.value.find((option) => option.value === row.businessRole)?.label;

        // 如果在businessRoleOptions中没有找到，尝试从缓存中获取
        if (!roleLabel && row.businessRole) {
          roleLabel = getRoleNameById(row.businessRole);
          // 如果从缓存获取的结果和原始ID相同，说明没有找到对应的角色名称
          if (roleLabel === row.businessRole?.toString()) {
            roleLabel = null;
          }
        }

        // 最终的显示文本
        roleLabel = roleLabel || row.businessRole || "未设置";
        return h(
          NTag,
          {
            type: "info",
            size: "large",
            bordered: false,
          },
          { default: () => roleLabel }
        );
      },
    },
    {
      title: "数据范围",
      key: "dataRangeNames",
      minWidth: 200,
      render(row) {
        const isEditing = editingRowId.value === row.id;

        if (isEditing) {
          // 编辑模式：显示已选择的机构标签和选择按钮
          const selectedOrgs = row._selectedDataRangeOrgs || [];

          return h(
            "div",
            {
              style: {
                display: "flex",
                flexDirection: "column",
                gap: "8px",
              },
            },
            [
              // 已选择的机构标签区域
              selectedOrgs.length > 0 ? h(
                "div",
                {
                  style: {
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "4px",
                    maxHeight: "60px",
                    overflowY: "auto",
                  },
                },
                selectedOrgs.map((org) =>
                  h(
                    NTag,
                    {
                      key: org.id,
                      type: "info",
                      size: "large",
                      closable: true,
                      onClose: () => removeSelectedOrg(row, org.id),
                      style: {
                        fontSize: "14px",
                      },
                    },
                    { default: () => org.orgName }
                  )
                )
              ) : h(
                "div",
                {
                  style: {
                    color: "#999",
                    fontSize: "14px",
                    padding: "4px 0",
                  },
                },
                "未选择机构"
              ),
              // 操作按钮区域
              h(
                "div",
                {
                  style: {
                    display: "flex",
                    gap: "8px",
                  },
                },
                [
                  h(
                    NButton,
                    {
                      size: "large",
                      type: "primary",
                      ghost: true,
                      onClick: () => openDataRangeSelector(row),
                    },
                    { default: () => "选择机构" }
                  ),
                  selectedOrgs.length > 0 ? h(
                    NButton,
                    {
                      size: "small",
                      type: "error",
                      ghost: true,
                      onClick: () => clearRowDataRange(row),
                    },
                    { default: () => "清空" }
                  ) : null,
                ].filter(Boolean)
              ),
            ]
          );
        }

        if (!row.dataRange) {
          return h("span", { style: { color: "#999" } }, "未设置");
        }

        // 处理数据范围显示逻辑 - 以tag形式显示
        const names = row.dataRangeNames || row.dataRange;
        const orgNames = names ? names.split(",").map((name) => name.trim()) : [];

        if (orgNames.length === 0) {
          return h("span", { style: { color: "#999" } }, "未设置");
        }

        // 显示前5个机构作为标签，超过5个则显示"更多"标签
        const maxDisplayCount = 5;
        const displayNames = orgNames.slice(0, maxDisplayCount);
        const totalCount = orgNames.length;

        const elements = [];

        // 添加机构标签
        displayNames.forEach((name) => {
          elements.push(
            h(
              NTag,
              {
                type: "success",
                size: "large",
                bordered: false,
                style: {
                  fontSize: "12px",
                  margin: "1px",
                },
              },
              { default: () => name }
            )
          );
        });

        // 如果机构数量超过显示数量，添加"更多"标签
        if (totalCount > maxDisplayCount) {
          elements.push(
            h(
              NTag,
              {
                type: "info",
                size: "large",
                bordered: false,
                style: {
                  fontSize: "12px",
                  margin: "1px",
                },
              },
              { default: () => `+${totalCount - maxDisplayCount}个` }
            )
          );
        }

        return h(
          "div",
          {
            style: {
              display: "flex",
              alignItems: "center",
              gap: "4px",
              flexWrap: "wrap",
            },
            title: names, // 完整的机构名称作为tooltip
          },
          elements
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      align: "center",
      render: (row) => {
        const isEditing = editingRowId.value === row.id;

        if (isEditing) {
          return h(
            "div",
            {
              style: {
                display: "flex",
                justifyContent: "center",
                gap: "8px",
              },
            },
            [
              h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color: "#18a058",
                    fontSize: "18px",
                  },
                  onClick: () => handleSaveEdit(row),
                  title: "保存",
                },
                [
                  h(
                    NIcon,
                    { size: 18 },
                    { default: () => h(CheckmarkOutlineIcon) }
                  ),
                ]
              ),
              h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color: "#d03050",
                    fontSize: "18px",
                  },
                  onClick: () => handleCancelEdit(row),
                  title: "取消",
                },
                [h(NIcon, { size: 18 }, { default: () => h(CloseOutlineIcon) })]
              ),
            ]
          );
        }

        return h(
          "div",
          {
            style: {
              display: "flex",
              justifyContent: "center",
              gap: "8px",
            },
          },
          [
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "#18a058",
                  fontSize: "18px",
                },
                onClick: () => handleEditRow(row),
                title: "编辑",
              },
              [h(NIcon, { size: 18 }, { default: () => h(CreateOutlineIcon) })]
            ),
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "#d03050",
                  fontSize: "18px",
                },
                onClick: () => handleDeleteMember(row.id),
                title: "删除",
              },
              [h(NIcon, { size: 18 }, { default: () => h(TrashOutlineIcon) })]
            ),
          ]
        );
      },
    },
  ];
}
