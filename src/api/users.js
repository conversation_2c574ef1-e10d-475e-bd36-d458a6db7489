import { doGet, doPost, doPut } from '@/utils/requests'

export function getDepartments(page = 1, size = 500) {
  return doGet('/system/department?is_limit=false', { page, size })
}

export function getDepartmentMembers(deptId, page = 1, size = 50) {
  return doGet(`/system/department/members/${deptId}`, { page, size })
}

export function getRoles() {
  return doGet('/system/role')
}

export function getUserDetails(userId) {
  return doGet(`/system/user/${userId}`)
}

export function createUser(userData) {
  return doPost('/system/user', userData)
}

export function updateUser(userData) {
  return doPut('/system/user', userData)
}

// Add more API functions as needed
