<template>
  <div class="router-test-container">
    <n-card title="路由模式测试">
      <n-space vertical>
        <n-alert type="info">
          <template #header>当前路由模式</template>
          <p>环境：{{ currentMode }}</p>
          <p>路由模式：{{ routerMode }}</p>
          <p>当前URL：{{ currentUrl }}</p>
        </n-alert>

        <n-divider />

        <n-space>
          <n-button type="primary" @click="testAppRoute">
            测试应用路由 (/app/index/1)
          </n-button>
          <n-button type="default" @click="testLoginRoute">
            测试登录路由 (/login)
          </n-button>
          <n-button type="default" @click="testHomeRoute">
            测试首页路由 (/)
          </n-button>
        </n-space>

        <n-divider />

        <n-card title="路由测试说明" size="small">
          <ul>
            <li><strong>开发环境</strong>：使用 History 模式，URL 格式为 <code>/app/index/1</code></li>
            <li><strong>生产环境</strong>：使用 Hash 模式，URL 格式为 <code>/#/app/index/1</code></li>
            <li>Hash 模式可以避免 CDN 部署时的 404 问题</li>
            <li>点击上方按钮测试不同路由的跳转</li>
          </ul>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NCard, NSpace, NButton, NAlert, NDivider } from 'naive-ui'

const router = useRouter()
const route = useRoute()

const currentUrl = ref('')

// 当前环境模式
const currentMode = computed(() => {
  return import.meta.env.MODE
})

// 路由模式
const routerMode = computed(() => {
  return import.meta.env.MODE === 'production' ? 'Hash模式' : 'History模式'
})

// 更新当前URL
const updateCurrentUrl = () => {
  currentUrl.value = window.location.href
}

onMounted(() => {
  updateCurrentUrl()
  // 监听路由变化
  router.afterEach(() => {
    updateCurrentUrl()
  })
})

// 测试应用路由
const testAppRoute = () => {
  router.push('/app/index/1')
}

// 测试登录路由
const testLoginRoute = () => {
  router.push('/login')
}

// 测试首页路由
const testHomeRoute = () => {
  router.push('/')
}
</script>

<style scoped>
.router-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

ul {
  margin: 0;
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
}
</style>
