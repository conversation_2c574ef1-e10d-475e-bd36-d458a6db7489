<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-content">
        <div class="login-form-section">
          <div class="login-logo">
            <img
              src="@/assets/images/jwd-logo.png"
              alt="Logo"
              style="width: 200px; height: auto"
            />
          </div>
          <div class="login-header">
            <h2>欢迎使用经纬度·云效协同办公系统</h2>
          </div>
          <div class="login-form">
            <div class="input-item">
              <div class="input-wrapper">
                <n-input
                  v-model:value="username"
                  placeholder="用户名"
                  autocomplete="off"
                  :autofocus="true"
                  :input-props="{ style: 'text-align: left' }"
                >
                  <template #prefix>
                    <n-icon><person-outline /></n-icon>
                  </template>
                </n-input>
              </div>
            </div>
            <div class="input-item">
              <div class="input-wrapper">
                <n-input
                  v-model:value="password"
                  type="password"
                  placeholder="密码"
                  show-password-on="click"
                  autocomplete="new-password"
                  @keyup.enter="handleLogin"
                  :input-props="{ style: 'text-align: left' }"
                >
                  <template #prefix>
                    <n-icon><lock-closed-outline /></n-icon>
                  </template>
                </n-input>
              </div>
            </div>
            <div class="login-actions">
              <n-checkbox v-model:checked="rememberMe">记住我</n-checkbox>
              <n-button type="primary" class="login-button" @click="handleLogin"
                >登录</n-button
              >
            </div>
          </div>
        </div>
        <div class="wecom-login-section">
          <n-spin size="large" :show="isLoading">
            <div ref="wecomLoginContainer"></div>
          </n-spin>
        </div>
      </div>
    </div>
    <footer class="footer">
      <div class="footer-content">
        <p>© 2025 山东沃尔思信息科技有限公司 版权所有</p>
        <p>ICP备案号: 鲁ICP备2022047615号-1</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { NInput, NButton, NCheckbox, NIcon, NSpin } from "naive-ui";
import { PersonOutline, LockClosedOutline } from "@vicons/ionicons5";
import { useRouter, useRoute } from "vue-router";
import { doPost } from "@/utils/requests";
import CryptoJS from "crypto-js";
import message from "@/utils/messages";
import { useMainStore } from "@/stores/mainStore";
import * as wecom from "@wecom/jssdk";

const router = useRouter();
const route = useRoute();
const mainStore = useMainStore();

const username = ref("");
const password = ref("");
const rememberMe = ref(false);

const encryptPassword = (pwd) => {
  const salt = "***#17600620312#";
  const key = CryptoJS.enc.Utf8.parse(salt);
  const iv = CryptoJS.enc.Utf8.parse(salt.slice(0, 16)); // 使用salt的前16个字符作为IV
  const encrypted = CryptoJS.AES.encrypt(pwd, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};

const decryptPassword = (encryptedPwd) => {
  const salt = "***#17600620312#";
  const key = CryptoJS.enc.Utf8.parse(salt);
  const iv = CryptoJS.enc.Utf8.parse(salt.slice(0, 16));
  const decrypted = CryptoJS.AES.decrypt(encryptedPwd, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
};

const handleLogin = async () => {
  try {
    let loginPassword = password.value;
    if (rememberMe.value) {
      // 如果是"记住我"，我们存储加密后的密码
      const encryptedPassword = encryptPassword(password.value);
      localStorage.setItem("rememberedUsername", username.value);
      localStorage.setItem("rememberedPassword", encryptedPassword);
    } else {
      localStorage.removeItem("rememberedUsername");
      localStorage.removeItem("rememberedPassword");
    }

    // 始终使用未加密的密码进行登录
    const response = await doPost("/system/login", {
      username: username.value,
      passwd: encryptPassword(password.value),
    });

    // 检查响应是否成功
    if (response.code === 200 && response.data) {
      // 存储 accessToken 到 localStorage
      localStorage.setItem("access_token", response.data.accessToken);
      localStorage.setItem("isAuthenticated", "true");

      // 存储用户信息
      const userData = {
        nickname: response.data.nickname,
        // 添加其他需要的用户信息
      };

      // 使用 mainStore 设置用户信息
      mainStore.setUser(userData);

      console.log("登录成功，准备跳转到首页");

      // 登录成功后跳转到首页
      router.push("/");
    } else {
      // 如果响应不符合预期，抛出错误
      throw new Error(response.message || "登录失败");
    }
  } catch (error) {
    console.error("Login failed:", error);
    message.error(error.message || "登录失败，请稍后重试");
  }
};

const isLoading = ref(true);
const wecomLoginContainer = ref(null);

const isDebugMode = ref(false);

const checkDebugMode = () => {
  const debugParam = route.query.debug;
  isDebugMode.value = debugParam === "true" || debugParam === "1";
  if (isDebugMode.value) {
    document.body.classList.add("debug-mode");
  } else {
    document.body.classList.remove("debug-mode");
  }
};

onMounted(() => {
  checkDebugMode();
  // 创建企业微信登录面板
  if (wecomLoginContainer.value) {
    wecom.createWWLoginPanel({
      el: wecomLoginContainer.value,
      params: {
        login_type: "CorpApp",
        appid: "ww06216d5138ca4db7",
        agentid: "1000096",
        redirect_uri: "https://lal.vooice.tech/",
        state: "loginState",
        redirect_type: "callback",
      },
      onCheckWeComLogin({ isWeComLogin }) {
        if (isWeComLogin) {
        }
      },
      onLoginSuccess({ code }) {
        doPost(`/system/wecom/login?code=${code}`)
          .then((response) => {
            // 根据响应结构获取accessToken
            let accessToken;
            let userData = { nickname: "企业微信用户" };

            // 处理不同的响应结构
            if (response.data && response.data.accessToken) {
              // 直接在data中
              accessToken = response.data.accessToken;
              userData.nickname = response.data.nickname || "企业微信用户";
            } else if (
              response.data &&
              response.data.data &&
              response.data.data.accessToken
            ) {
              // 嵌套在data.data中
              accessToken = response.data.data.accessToken;
              userData.nickname = response.data.data.nickname || "企业微信用户";
            } else if (response.accessToken) {
              // 直接在response根级别
              accessToken = response.accessToken;
              userData.nickname = response.nickname || "企业微信用户";
            } else {
              console.error("无法从响应中获取accessToken:", response);
              throw new Error("登录响应格式不正确");
            }

            // 存储 accessToken 到 localStorage
            localStorage.setItem("access_token", accessToken);
            localStorage.setItem("isAuthenticated", "true");

            // 使用 mainStore 设置用户信息
            mainStore.setUser(userData);

            // 获取菜单数据
            mainStore
              .fetchMenus()
              .then(() => {
                // 使用 router 进行导航而不是直接修改 location
                router.push("/");
              })
              .catch((err) => {
                console.error("获取菜单数据失败，但仍然跳转到首页:", err);
                // 即使获取菜单失败，也跳转到首页
                router.push("/");
              });
          })
          .catch((error) => {
            console.error("企业微信登录失败:", error);
            // 显示更详细的错误信息
            if (error.response) {
              // 服务器返回了错误响应
              const errorMsg =
                error.response.data?.message ||
                `服务器错误 (${error.response.status})`;
              message.error(`登录失败: ${errorMsg}`);
            } else if (error.request) {
              // 请求发送了但没有收到响应
              message.error("登录失败: 服务器无响应，请检查网络连接");
            } else {
              // 请求设置时出错
              message.error(`登录失败: ${error.message || "未知错误"}`);
            }
            location.reload(); 
          });
      },
      onLoginFail(err) {
        console.log(err);
      },
    });

    // 在创建完成后隐藏 loading
    isLoading.value = false;
  }

  // 检查是否有保存的登录信息
  const savedUsername = localStorage.getItem("rememberedUsername");
  const savedPassword = localStorage.getItem("rememberedPassword");

  if (savedUsername && savedPassword) {
    username.value = savedUsername;
    // 解密保存的密码
    password.value = decryptPassword(savedPassword);
    rememberMe.value = true;
  }

  // 清除自动填充样式
  setTimeout(() => {
    const inputs = document.querySelectorAll("input");
    inputs.forEach((input) => {
      input.style.backgroundColor = "transparent";
      input.style.boxShadow = "none";
    });
  }, 100);
});

watch(
  () => route.query,
  () => {
    checkDebugMode();
  },
  { deep: true }
);
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  min-height: 100vh;
  height: 100vh;
  background-color: #f0f4f9;
  overflow: hidden; /* 修改这里 */
  box-sizing: border-box; /* 添加这行 */
  padding: 20px 0; /* 添加这行，给顶部和底部一些内边距 */
}

.login-box {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 40px;
  width: 90%;
  max-width: 1260px;
  height: auto;
  max-height: calc(
    100vh - 120px
  ); /* 修改这里，减去顶部和底部的内边距以及一些额外空间 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: auto;
  overflow-y: auto;
  box-sizing: border-box; /* 添加这行 */
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  font-size: 28px;
  color: #333;
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 458px;
}

.input-item {
  width: 100%;
  max-width: 458px;
  margin-bottom: 20px;
}

.input-wrapper {
  width: 100%;
}

.login-actions {
  width: 100%;
  max-width: 458px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.login-button {
  width: 140px;
  height: 50px;
  font-size: 18px;
}

.footer {
  width: 100%;
  padding: 10px 0;
  background-color: #f0f4f9;
  text-align: center;
}

.footer-content {
  font-size: 14px;
  color: #606266;
}

.footer-content p {
  margin: 5px 0;
}

/* 添加响应式设计 */
@media (max-height: 800px) {
  .login-box {
    padding: 20px;
    max-height: calc(100vh - 80px); /* 调整最大高度 */
  }

  .login-header h2 {
    font-size: 24px;
  }

  .input-item {
    margin-bottom: 15px;
  }

  .login-button {
    height: 40px;
    font-size: 16px;
  }
}

/* 覆盖 NaiveUI 默认样式 */
:deep(input:-webkit-autofill),
:deep(input:-webkit-autofill:hover),
:deep(input:-webkit-autofill:focus),
:deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: #333 !important;
}

:deep(.n-input) {
  max-width: 100%;
  background-color: transparent !important;
}

:deep(.n-input__input) {
  height: 56px; /* 稍微增加高度 */
  font-size: 18px; /* 增加字体大小 */
  background-color: transparent !important;
}

:deep(.n-input__input-el) {
  height: 56px; /* 确保输入元素高度与外层一致 */
  line-height: 56px; /* 设置行高等于高度，使文本垂直居中 */
  font-size: 18px; /* 确保字体大小一致 */
  background-color: transparent !important;
  padding-top: 0; /* 移除顶部内边距 */
  padding-bottom: 0; /* 移除底部内边距 */
}

:deep(.n-input__prefix) {
  margin-right: 12px; /* 稍微增加图标和文本之间的距离 */
}

:deep(.n-input__prefix-icon) {
  font-size: 20px; /* 增加图标大小 */
}

/* 调整复选框和按钮样式以保持一致性 */
:deep(.n-checkbox) {
  font-size: 16px;
}

.login-button {
  width: 140px;
  height: 56px; /* 调整按钮高度与输入框一致 */
  font-size: 18px;
}

/* 添加响应式设计 */
@media (max-height: 800px) {
  :deep(.n-input__input),
  :deep(.n-input__input-el) {
    height: 48px; /* 在较小屏幕上稍微减小高度 */
    line-height: 48px;
    font-size: 16px; /* 在较小屏幕上稍微减小字体大小 */
  }

  :deep(.n-input__prefix-icon) {
    font-size: 18px; /* 在较小屏幕上稍微减小图标大小 */
  }

  .login-button {
    height: 48px;
    font-size: 16px;
  }
}

.login-content {
  display: flex;
  justify-content: space-between;
  align-items: stretch; /* 修改这里，确保子元素高度一致 */
  width: 100%;
  height: 100%; /* 添加这行，确保内容占满整个高度 */
}

.login-form-section {
  flex: 0 0 60%;
  max-width: 60%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 20px; /* 添加右侧内边距 */
}

.login-logo,
.login-header,
.login-form {
  width: 100%;
  text-align: center;
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-item,
.login-actions {
  width: 100%;
}

.wecom-login-section {
  flex: 0 0 40%;
  max-width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-left: 10px; /* 添加左侧内边距 */
  border-left: 1px solid #e0e0e0;
  height: 100%;
}

/* 修改 n-spin 相关样式 */
.wecom-login-section :deep(.n-spin-container) {
  width: 100%;
  height: 100%;
  margin: 0; /* 移除可能的外边距 */
  padding: 0; /* 移除可能的内边距 */
}

.wecom-login-section :deep(.n-spin-content) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0; /* 移除可能的外边距 */
  padding: 0; /* 移除可能的内边距 */
}

/* 确保 wecomLoginContainer 内的元素居中 */
.wecom-login-section #wecomLoginContainer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0; /* 移除可能的外边距 */
  padding: 0; /* 移除可能的内边距 */
}

/* 移除 wecom-login-section 内部元素的垂直间距 */
.wecom-login-section > * {
  margin: 0; /* 移除垂直间距 */
}

@media (max-width: 1024px) {
  .login-content {
    flex-direction: column;
    align-items: center;
    height: auto; /* 在小屏幕上允许高度自适应 */
  }

  .login-form-section,
  .wecom-login-section {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 0;
    padding-left: 0;
  }

  .login-form-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #e0e0e0; /* 在小屏幕上添加底部边框 */
    padding-bottom: 40px; /* 添加底部内边距 */
  }

  .wecom-login-section {
    border-left: none;
    padding-top: 40px;
    height: auto;
  }
}

/* 修改调试模式样式 */
:global(.debug-mode) div {
  position: relative !important;
}

:global(.debug-mode) div::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 0.2px solid rgba(128, 128, 128, 0.5);
  pointer-events: none;
  z-index: 9999;
}

:global(.debug-mode) div::before {
  content: attr(class);
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  font-size: 10px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  box-sizing: border-box;
  z-index: 10000;
  pointer-events: none;
}

/* 调整输入框样式 */
:deep(.n-input__placeholder) {
  text-align: left;
  padding-left: 40px; /* 调整此值以匹配图标和文本之间的距离 */
}

:deep(.n-input__input:not(:placeholder-shown)) {
  text-align: left;
  padding-left: 40px; /* 确保输入的文本与 placeholder 对齐 */
}

:deep(.n-input__input:focus::placeholder) {
  color: transparent;
}
</style>
