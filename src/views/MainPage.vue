<script setup>
import {
  NLayout,
  NLayoutHeader,
  NLayoutContent,
  NImage,
  NDropdown,
  NSpace,
  NIcon,
  NPopover,
} from "naive-ui";
import { PersonCircleOutline, Apps } from "@vicons/ionicons5";
import { useRouter } from "vue-router";
import { useMainStore } from "@/stores/mainStore";
import logoImage from "@/assets/images/jwd-logo.png";
import { onMounted, computed, ref } from "vue";

const router = useRouter();
const mainStore = useMainStore();

// 导入 iconMap
import { iconMap } from "@/stores/mainStore";

// 渲染图标函数
function renderIcon(iconName) {
  if (!iconName) return null;
  const IconComponent = iconMap[iconName] || null;
  if (!IconComponent) return null;
  // 返回图标组件，用于传递给 n-icon 的 component 属性
  return IconComponent;
}

// 添加当前选中的菜单状态
const currentMenu = ref("dashboard");

// 登录状态检查
onMounted(async () => {
  // 检查 access_token 是否存在
  const accessToken = localStorage.getItem("access_token");
  if (!accessToken) {
    router.push("/login");
    return;
  }

  // 如果 store 中没有用户信息，尝试获取
  if (!mainStore.isUserLoggedIn) {
    const isLoggedIn = await mainStore.checkLoginStatus();
    if (!isLoggedIn) {
      router.push("/login");
      return;
    }
  }

  // 获取菜单数据
  if (mainStore.getMenus.length === 0) {
    await mainStore.fetchMenus();
  }
});

// 用户菜单选项
const userMenuOptions = [
  {
    label: "重置密码",
    key: "reset-password",
  },
  {
    label: "退出系统",
    key: "logout",
  },
];

// 用户菜单处理
const handleUserMenuSelect = async (key) => {
  if (key === "reset-password") {
    console.log("重置密码");
  } else if (key === "logout") {
    console.log("开始退出系统");
    // 等待 logout 方法完成
    await mainStore.logout();
    console.log("退出系统完成，准备跳转到登录页");
    // 确保在 logout 完成后再跳转
    router.push("/login");
  }
};

// 问候语计算
const greeting = computed(() => {
  const hour = new Date().getHours();
  if (hour >= 21 || hour < 6) return "您辛苦";
  if (hour >= 18) return "晚上好";
  if (hour >= 13) return "下午好";
  if (hour >= 11) return "中午好";
  if (hour >= 8) return "上午好";
  return "早上好";
});

// 应用切换菜单选项
const appSwitchOptions = computed(() => {
  // 尝试从 localStorage 中直接获取菜单数据
  const storedMenus = localStorage.getItem("menus");
  if (storedMenus) {
    try {
      const parsedMenus = JSON.parse(storedMenus);
      // 过滤出 parentId 为 1 的菜单
      const directTopLevelMenus = parsedMenus.filter(
        (menu) => menu.parentId === 1
      );
      console.log("从 localStorage 中直接获取的顶级菜单:", directTopLevelMenus);

      // 不过滤 visible 属性，显示所有顶级菜单
      return directTopLevelMenus.map((menu) => {
        // 构建完整路径
        const menuPath = menu.menuPath || "";
        const fullPath = menuPath.startsWith("/") ? menuPath : `/${menuPath}`;

        return {
          label: menu.menuLabel,
          key: fullPath,
          icon: menu.menuIcon ? renderIcon(menu.menuIcon) : null,
          viewPath: menu.viewPath,
          id: menu.id, // 添加菜单ID
          color: currentMenu.value === fullPath ? "#18A058" : "#1677ff",
        };
      });
    } catch (error) {
      console.error("解析菜单数据失败:", error);
      return [];
    }
  }

  // 如果没有从 localStorage 中获取到数据，则使用 store 中的数据
  const topLevelMenus = mainStore.getTopLevelMenus;
  console.log("从 store 中获取的顶级菜单项:", topLevelMenus);

  return topLevelMenus.map((menu) => ({
    label: menu.label,
    key: menu.key,
    icon: menu.icon,
    viewPath: menu.viewPath,
    id: menu.id, // 添加菜单ID
    color: currentMenu.value === menu.key ? "#18A058" : "#1677ff",
  }));
});

// 修改应用切换菜单控制
const showAppMenu = ref(false);
const handleAppSwitchSelect = async (key, menuItem) => {
  showAppMenu.value = false;
  currentMenu.value = key;

  // 如果 key 为 null 或 undefined，则跳转到首页
  if (!key) {
    console.log("菜单项的 key 为空，跳转到首页");
    router.push("/");
    return;
  }

  console.log("尝试切换到:", key);

  // 获取当前点击的菜单项
  const clickedMenu =
    menuItem || appSwitchOptions.value.find((item) => item.key === key);
  console.log("点击的菜单项:", clickedMenu);

  if (clickedMenu && clickedMenu.id) {
    // 如果有菜单ID，则路由到AppIndex页面
    console.log("导航到AppIndex页面，菜单ID:", clickedMenu.id);
    // 使用Vue Router进行导航，支持Hash模式
    console.log("将跳转到菜单页面，菜单ID:", clickedMenu.id);
    router.push({
      path: `/app/index/${clickedMenu.id}`,
      replace: true,
    });
  } else {
    // 直接跳转到目标路径
    console.log("导航到:", key);
    router.push({
      path: key,
      replace: true,
    });
  }
};
</script>

<template>
  <n-layout position="absolute">
    <n-layout-header class="header">
      <div class="logo-container" @click="router.push('/')">
        <n-image
          :src="logoImage"
          width="100"
          preview-disabled
          style="cursor: pointer"
        />
      </div>
      <div class="user-info">
        <n-space align="center" :size="20">
          <div class="app-menu-wrapper">
            <n-popover
              trigger="click"
              placement="bottom"
              :show="showAppMenu"
              @update:show="showAppMenu = $event"
            >
              <template #trigger>
                <div class="app-switch-trigger">
                  <n-icon
                    :size="28"
                    :component="Apps"
                    :color="showAppMenu ? '#18A058' : '#333'"
                  />
                </div>
              </template>
              <div class="app-menu-grid">
                <div
                  v-for="option in appSwitchOptions"
                  :key="option.key"
                  class="app-menu-item"
                  :class="{
                    'app-menu-item-active': currentMenu === option.key,
                  }"
                  @click="handleAppSwitchSelect(option.key, option)"
                >
                  <n-icon
                    v-if="option.icon"
                    :component="option.icon"
                    class="menu-icon"
                    size="28"
                  />
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </n-popover>
          </div>

          <div class="greeting">
            {{ greeting }}，{{ mainStore.getUser?.nickname || "未登录" }}
          </div>
          <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
            <div class="user-dropdown-trigger">
              <n-icon :component="PersonCircleOutline" size="28" />
            </div>
          </n-dropdown>
        </n-space>
      </div>
    </n-layout-header>
    <n-layout-content>
      <router-view></router-view>
    </n-layout-content>
  </n-layout>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
  z-index: 1000;
}

.logo-container {
  margin-top: 12px;
}

.main-content {
  height: calc(100vh - 64px);
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.content-wrapper) {
  position: absolute;
  top: 14px;
  left: 14px;
  right: 14px;
  bottom: 14px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Webkit 浏览器的滚动条样式 */
:deep(.content-wrapper::-webkit-scrollbar) {
  width: 6px;
  background-color: transparent;
}

:deep(.content-wrapper::-webkit-scrollbar-thumb) {
  background-color: transparent;
  border-radius: 3px;
  transition: background-color 0.3s;
}

/* 鼠标悬停或滚动时显示滚动条 */
:deep(.content-wrapper:hover::-webkit-scrollbar-thumb),
:deep(.content-wrapper:active::-webkit-scrollbar-thumb) {
  background-color: #d9d9d9;
}

/* 滚动条轨道 */
:deep(.content-wrapper::-webkit-scrollbar-track) {
  background-color: transparent;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown-trigger {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.user-dropdown-trigger:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.app-menu-wrapper {
  position: relative;
}

:deep(.n-popover) {
  padding: 0 !important;
  border-radius: 12px !important;
}

:deep(.n-popover-content-wrapper) {
  transform-origin: top center !important;
}

.app-menu-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 8px;
  width: 360px;
}

.app-menu-item {
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: transparent;
}

.app-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.app-menu-item .menu-icon,
.app-menu-item .n-icon {
  margin-bottom: 8px;
  font-size: 24px;
  transition: transform 0.3s;
}

.app-menu-item:hover .menu-icon,
.app-menu-item:hover .n-icon {
  transform: scale(1.1);
}

.app-menu-item span {
  font-size: 12px;
  color: #333;
  text-align: center;
  white-space: nowrap;
  margin-top: 4px;
}

.app-switch-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.app-switch-trigger:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.app-switch-icon {
  transition: transform 0.3s;
}

.app-switch-icon.active {
  transform: rotate(180deg);
  color: #18a058 !important;
}

.placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
}
</style>
