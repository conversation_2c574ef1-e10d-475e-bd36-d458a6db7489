<template>
  <div class="customer-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">创建日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">客户类型</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.customerType"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in customerCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">成交状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dealStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dealStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">所属单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="filterForm.showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon>
                  <component :is="BuildingIcon" />
                </n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <div
              v-if="filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.ownerOrgs"
                :key="org.id"
                :bordered="false"
                type="success"
                size="small"
                closable
                @close="() => removeOrg(org)"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon>
              <component :is="RefreshOutlineIcon" />
            </n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon>
              <component :is="AddOutlineIcon" />
            </n-icon>
          </template>
          新增客户
        </n-button>

        <n-button type="info" @click="showCustomerSelector" round>
          <template #icon>
            <n-icon>
              <component :is="SearchOutlineIcon" />
            </n-icon>
          </template>
          选择客户
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/customer-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入客户名称或联系电话"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <component :is="SearchOutlineIcon" />
            </n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row) => row.id"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      :style="
        isEditDialogMaximized
          ? { width: '50%', height: '90%' }
          : { width: '600px' }
      "
      :mask-closable="false"
      transform-origin="center"
    >
      <template #header-extra>
        <n-button quaternary circle @click="toggleEditDialogSize">
          <template #icon>
            <n-icon>
              <component
                :is="
                  isEditDialogMaximized
                    ? ContractOutlineIcon
                    : ExpandOutlineIcon
                "
              />
            </n-icon>
          </template>
        </n-button>
      </template>
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="isEditDialogMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item label="客户类型" path="customerType">
              <n-select
                v-model:value="form.customerType"
                :options="
                  customerCategoryOptions.filter((item) => item.value !== null)
                "
                @update:value="handleCustomerTypeChange"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户名称" path="customerName">
              <n-input
                v-model:value="form.customerName"
                placeholder="请输入客户名称"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="isEditDialogMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item
              :label="
                form.customerType === 'institutional'
                  ? '统一社会信用代码'
                  : '身份证号码'
              "
              path="customerIdCode"
            >
              <n-input
                v-model:value="form.customerIdCode"
                :placeholder="
                  form.customerType === 'institutional'
                    ? '请输入统一社会信用代码'
                    : '请输入身份证号码'
                "
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="手机号码" path="mobile">
              <n-input
                v-model:value="form.mobile"
                placeholder="请输入手机号码"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="isEditDialogMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item label="所属单位" path="ownerOrgName">
              <n-button
                type="primary"
                ghost
                @click="form.showOrgSelector = true"
                style="width: 100%; justify-content: flex-start"
              >
                <template #icon>
                  <n-icon>
                    <component :is="BuildingIcon" />
                  </n-icon>
                </template>
                {{ form.ownerOrgName || "请选择所属单位" }}
              </n-button>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售顾问" path="ownerSellerName">
              <member-selector
                :model-value="sellerModelValue"
                @update:model-value="handleSellerSelect"
                mode="single"
                label="请选择销售顾问"
                width="100%"
                :dept-id="form.ownerOrgId"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="isEditDialogMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item label="成交状态" path="dealStatus">
              <n-select
                v-model:value="form.dealStatus"
                :options="
                  dealStatusOptions.filter((item) => item.value !== null)
                "
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户地址" path="address">
              <n-input
                v-model:value="form.address"
                placeholder="请输入客户地址"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 测试业务机构成员选择器 -->
        <n-grid :cols="isEditDialogMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item label="关联成员（测试）">
              <n-space direction="vertical" style="width: 100%">
                <n-space>
                  <n-button
                    type="primary"
                    ghost
                    @click="showMemberSelectorSingle"
                    size="small"
                  >
                    选择成员（单选）
                  </n-button>
                  <n-button
                    type="info"
                    ghost
                    @click="showMemberSelectorMultiple"
                    size="small"
                  >
                    选择成员（多选）
                  </n-button>
                  <n-button
                    v-if="form.selectedMembers.length > 0"
                    type="error"
                    ghost
                    @click="clearSelectedMembers"
                    size="small"
                  >
                    清空选择
                  </n-button>
                </n-space>

                <!-- 显示选中的成员 -->
                <div
                  v-if="form.selectedMembers.length > 0"
                  class="selected-members-display"
                >
                  <n-text depth="3"
                    >已选择 {{ form.selectedMembers.length }} 个成员：</n-text
                  >
                  <div class="member-tags">
                    <n-tag
                      v-for="member in form.selectedMembers"
                      :key="member.id"
                      type="success"
                      size="small"
                      closable
                      @close="removeMemberFromSelection(member)"
                    >
                      {{ member.name }} ({{ member.agentId }})
                    </n-tag>
                  </div>
                </div>
                <div v-else>
                  <n-text depth="3">未选择成员</n-text>
                </div>
              </n-space>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <!-- 占位，保持布局平衡 -->
          </n-grid-item>
        </n-grid>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 详情弹窗 -->
    <customer-detail-modal
      :visible="detailDialogVisible"
      @update:visible="(val) => (detailDialogVisible = val)"
      :id="currentDetailId"
    />

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 筛选条件的业务机构选择器 -->
    <biz-org-selector
      v-model:visible="filterForm.showOrgSelector"
      title="选择所属单位"
      business-permission="can_sell"
      :multiple="true"
      @select="handleFilterOrgSelect"
      @cancel="handleFilterOrgCancel"
    />

    <!-- 表单中的业务机构选择器 -->
    <biz-org-selector
      v-model:visible="form.showOrgSelector"
      title="选择所属单位"
      :single="true"
      @select="handleFormOrgSelect"
      @cancel="handleFormOrgCancel"
    />

    <!-- 业务机构成员选择器（测试） -->
    <biz-org-member-selector
      v-model:visible="memberSelectorVisible"
      :mode="memberSelectorMode"
      :initial-org-id="memberSelectorInitialOrgId"
      :title="memberSelectorTitle"
      @select="handleMemberSelected"
      @cancel="handleMemberSelectCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h, markRaw } from "vue";
import messages from "@/utils/messages";
import customerApi from "@/api/customer";
import { NIcon, NTag, NSelect } from "naive-ui";
import {
  EyeOutline,
  CreateOutline,
  ContractOutline,
  ExpandOutline,
  RefreshOutline,
  AddOutline,
  SearchOutline,
} from "@vicons/ionicons5";
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate,
} from "@/utils/dateRange";
import MemberSelector from "@/components/users/MemberSelector.vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import BizOrgMemberSelector from "@/components/bizOrg/BizOrgMemberSelector.vue";
import { Building } from "@vicons/tabler";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const EyeOutlineIcon = markRaw(EyeOutline);
const CreateOutlineIcon = markRaw(CreateOutline);
const ContractOutlineIcon = markRaw(ContractOutline);
const ExpandOutlineIcon = markRaw(ExpandOutline);
const RefreshOutlineIcon = markRaw(RefreshOutline);
const AddOutlineIcon = markRaw(AddOutline);
const SearchOutlineIcon = markRaw(SearchOutline);
const BuildingIcon = markRaw(Building);
import FileUploadButton from "@/components/FileUploadButton.vue";
import CustomerDetailModal from "@/components/customer/CustomerDetailModal.vue";
import CustomerSelector from "@/components/customer/CustomerSelector.vue";

// 消息提示已通过 utils/messages.js 提供

// 状态变量
const tableRef = ref(null);
const formRef = ref(null);
const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref("新增订单");
const isEdit = ref(false);
const selectedRows = ref([]);

// 弹窗最大化状态
const isEditDialogMaximized = ref(true);

// 切换弹窗最大化/最小化
const toggleEditDialogSize = () => {
  isEditDialogMaximized.value = !isEditDialogMaximized.value;
};

// 日期范围选项已从utils/dateRange.js导入

// 车辆类别选项
const customerCategoryOptions = [
  { label: "不限", value: null },
  { label: "个人客户", value: "individual" },
  { label: "法人客户", value: "institutional" },
];

// 订单状态选项
const dealStatusOptions = [
  { label: "不限", value: null },
  { label: "未成交", value: "LEADS" },
  { label: "已成交", value: "CUSTOMER" },
];

// 筛选表单
const filterForm = reactive({
  dateRange: null,
  customDateRange: null,
  customerType: null, // 客户类型
  ownerOrgs: [], // 所属单位，改为数组以支持多选
  keywords: "", // 关键字搜索
  dealStatus: null, // 成交状态
  showOrgSelector: false, // 控制机构选择器显示
});

// 表单数据
const form = reactive({
  id: null,
  customerName: "",
  customerType: "individual", // 默认为个人客户
  customerIdCode: "", // 身份证号码
  mobile: "", // 手机号码
  ownerOrgId: null, // 所属单位ID
  ownerOrgName: "", // 所属单位名称
  ownerSellerId: null, // 销售顾问ID
  ownerSellerName: "", // 销售顾问姓名
  address: "",
  remark: "",
  dealStatus: "CUSTOMER", // 默认为已成交状态
  showOrgSelector: false, // 控制表单中的机构选择器显示
  selectedMembers: [], // 选中的成员列表（测试用）
});

// 业务机构成员选择器状态
const memberSelectorVisible = ref(false);
const memberSelectorMode = ref("single");
const memberSelectorInitialOrgId = ref(null);
const memberSelectorTitle = ref("选择业务机构成员");

// 表单验证规则
const rules = {
  customerName: {
    required: true,
    message: "请输入客户名称",
    trigger: ["blur", "input"],
  },
  customerType: {
    required: true,
    message: "请选择客户类型",
    trigger: ["blur", "change"],
  },
  customerIdCode: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error(
          form.customerType === "individual"
            ? "请输入身份证号码"
            : "请输入统一社会信用代码"
        );
      }
      return true;
    },
    trigger: ["blur", "input"],
  },
  mobile: {
    required: true,
    message: "请输入手机号码",
    trigger: ["blur", "input"],
  },
  ownerOrgName: {
    required: true,
    message: "请选择所属单位",
    trigger: ["blur", "input"],
  },
  ownerSellerName: {
    required: true,
    message: "请选择销售顾问",
    trigger: ["blur", "input"],
  },
};

// 数据列表
const customerData = ref([]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page;
    refreshData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    refreshData();
  },
});

// 表格列配置
const columns = [
  { type: "selection", width: 50 },
  { title: "创建日期", key: "createTime", width: 120 },

  {
    title: "客户类型",
    key: "customerType",
    width: 100,
    render(row) {
      const statusMap = {
        individual: { text: "个人客户", type: "info", color: "#2080f0" },
        institutional: { text: "法人客户", type: "success", color: "#18a058" },
      };

      const status = statusMap[row.customerType] || {
        text: "未知",
        type: "default",
        color: "#909399",
      };

      return h(
        NTag,
        {
          type: status.type,
          bordered: false,
          style: {
            padding: "2px 8px",
            fontWeight: "bold",
          },
        },
        { default: () => status.text }
      );
    },
  },
  {
    title: "客户名称",
    key: "customerName",
    width: 150,
  },
  {
    title: "联系方式",
    key: "mobile",
    width: 120,
  },
  {
    title: "所属单位",
    key: "ownerOrgName",
    width: 180,
  },
  {
    title: "销售顾问",
    key: "ownerSellerName",
    width: 100,
  },
  {
    title: "成交状态",
    key: "dealStatus",
    width: 100,
    render(row) {
      const statusMap = {
        LEADS: { text: "未成交", type: "warning", color: "#f0a020" },
        CUSTOMER: { text: "已成交", type: "success", color: "#18a058" },
      };

      const status = statusMap[row.dealStatus] || {
        text: "未知",
        type: "default",
        color: "#909399",
      };

      return h(
        NTag,
        {
          type: status.type,
          bordered: false,
          style: {
            padding: "2px 8px",
            fontWeight: "bold",
          },
        },
        { default: () => status.text }
      );
    },
  },
  {
    title: "备注",
    key: "remark",
    width: 150,
  },
  {
    title: "操作",
    key: "actions",
    width: 100,
    fixed: "right",
    align: "center",
    render: (row) => {
      return h(
        "div",
        { style: { display: "flex", justifyContent: "center", gap: "12px" } },
        [
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#2080f0",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleView(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(EyeOutlineIcon) })]
          ),
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#18a058",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleEdit(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
          ),
        ]
      );
    },
  },
];

// 表格数据
const filteredData = computed(() => {
  return customerData.value;
});

// 初始化
onMounted(() => {
  refreshData();
});

// 刷新数据
// 日期范围参数获取函数已从utils/dateRange.js导入

const refreshData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.pageNum,
      size: pagination.pageSize,
    };

    // 添加筛选条件
    if (filterForm.keywords) {
      params.keywords = filterForm.keywords;
    }

    // 处理日期范围
    if (filterForm.dateRange) {
      const dateRange = getDateRangeParams(
        filterForm.dateRange,
        filterForm.customDateRange
      );
      if (dateRange.startDate) params.startDate = dateRange.startDate;
      if (dateRange.endDate) params.endDate = dateRange.endDate;
    }

    // 处理客户类型
    if (filterForm.customerType) {
      params.customerType = filterForm.customerType;
    }

    // 处理所属单位
    if (filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0) {
      // 使用机构id作为机构代码，以逗号分隔的格式传入
      params.owner_org_code = filterForm.ownerOrgs
        .map((org) => org.id)
        .join(",");
    }

    // 处理成交状态
    if (filterForm.dealStatus) {
      params.dealStatus = filterForm.dealStatus;
    }

    // 调用API获取数据
    const response = await customerApi.getCustomerList(params);

    if (response.code === 200) {
      // 直接使用返回的数据列表
      customerData.value = response.data.list;

      // 更新分页信息
      pagination.itemCount = response.data.total;
      pagination.pageCount = response.data.pages;

      // messages.success('数据加载成功')
    } else {
      messages.error(response.message || "数据加载失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    messages.error("加载数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  handleDateChange(value, filterForm, handleSearch);
};

// 处理自定义日期变化
const handleCustomDateChange = (dates) => {
  handleCustomDate(dates, handleSearch);
};

// 处理查询
const handleSearch = () => {
  pagination.page = 1;
  refreshData();
};

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false;
  dialogTitle.value = "新增客户";

  // 重置表单
  const customerType = "individual"; // 默认为个人客户
  Object.assign(form, {
    id: null,
    customerName: "",
    customerType: customerType,
    customerIdCode: "",
    mobile: "",
    ownerOrgId: null,
    ownerOrgName: "",
    ownerSellerId: null,
    ownerSellerName: "",
    address: "",
    remark: "",
    dealStatus: customerType === "institutional" ? "LEADS" : "CUSTOMER", // 根据客户类型设置默认成交状态
    showOrgSelector: false,
    selectedMembers: [], // 重置选中的成员
  });

  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (id) => {
  try {
    loading.value = true;
    console.log("编辑客户:", id);

    // 调用API获取详细数据
    const response = await customerApi.getCustomerDetail(id);

    if (response.code === 200) {
      const apiData = response.data;

      isEdit.value = true;
      dialogTitle.value = "编辑客户";

      // 填充表单数据
      Object.assign(form, {
        id: apiData.id,
        customerName: apiData.customerName || "",
        customerType: apiData.customerType || "individual",
        customerIdCode: apiData.customerIdCode || "",
        mobile: apiData.mobile || "",
        ownerOrgId: apiData.ownerOrgId || null,
        ownerOrgName: apiData.ownerOrgName || "",
        ownerSellerId: apiData.ownerSellerId || null,
        ownerSellerName: apiData.ownerSellerName || "",
        address: apiData.address || "",
        remark: apiData.remark || "",
        dealStatus: apiData.dealStatus || "CUSTOMER",
        showOrgSelector: false,
        selectedMembers: [], // 编辑时重置选中的成员
      });

      dialogVisible.value = true;
    } else {
      messages.error(response.message || "获取客户数据失败");
    }
  } catch (error) {
    console.error("获取客户数据失败:", error);
    messages.error("获取客户数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 详情弹窗
const detailDialogVisible = ref(false);
const currentDetailId = ref(null);

// 处理查看
const handleView = (id) => {
  currentDetailId.value = id;
  detailDialogVisible.value = true;
};

// 客户选择器
const customerSelectorVisible = ref(false);

// 显示客户选择器
const showCustomerSelector = () => {
  customerSelectorVisible.value = true;
};

// 处理客户选择
const handleCustomerSelected = (customer) => {
  messages.success(`已选择客户: ${customer.customerName}`);
  console.log("选择的客户:", customer);

  // 这里可以根据需要处理选择的客户数据
  // 例如，可以将选择的客户数据填充到表单中
  Object.assign(form, {
    id: customer.id,
    customerName: customer.customerName,
    customerType: customer.customerType,
    customerIdCode: customer.customerIdCode,
    mobile: customer.mobile,
    ownerOrgId: customer.ownerOrgId,
    ownerOrgName: customer.ownerOrgName,
    ownerSellerId: customer.ownerSellerId,
    ownerSellerName: customer.ownerSellerName,
    address: customer.address,
    remark: customer.remark,
    dealStatus: customer.dealStatus,
    showOrgSelector: false,
  });

  // 打开编辑弹窗
  isEdit.value = true;
  dialogTitle.value = "编辑客户";
  dialogVisible.value = true;
};

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    try {
      loading.value = true;

      // 准备请求数据
      const data = {
        customerName: form.customerName,
        customerType: form.customerType,
        customerIdCode: form.customerIdCode,
        mobile: form.mobile,
        ownerOrgId: form.ownerOrgId,
        ownerOrgName: form.ownerOrgName,
        ownerSellerId: form.ownerSellerId,
        ownerSellerName: form.ownerSellerName,
        address: form.address || "",
        remark: form.remark || "",
        dealStatus: form.dealStatus,
      };

      // 如果是编辑模式，添加ID
      if (isEdit.value) {
        data.id = form.id;
      }

      // 调用保存API
      const response = isEdit.value
        ? await customerApi.updateCustomer(data)
        : await customerApi.addCustomer(data);

      if (response.code === 200) {
        messages.success(isEdit.value ? "更新成功" : "添加成功");
        dialogVisible.value = false;
        // 刷新数据
        refreshData();
      } else {
        messages.error(
          response.message || (isEdit.value ? "更新失败" : "添加失败")
        );
      }
    } catch (error) {
      console.error("保存失败:", error);
      messages.error("保存失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  });
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = customerData.value.filter((item) =>
    keys.includes(item.id)
  );
};

// 销售顾问选择器的计算属性
const sellerModelValue = computed(() => {
  if (!form.ownerSellerId || !form.ownerSellerName) return null;
  return {
    id: form.ownerSellerId,
    name: form.ownerSellerName,
  };
});

// 计算属性：选中的机构文本
const selectedOrgText = computed(() => {
  if (filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0) {
    if (filterForm.ownerOrgs.length === 1) {
      return filterForm.ownerOrgs[0].orgName;
    } else {
      return `已选择 ${filterForm.ownerOrgs.length} 个机构`;
    }
  }
  return "请选择所属单位";
});

// 处理筛选条件的机构选择
const handleFilterOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    // 多选模式，保存所有选中的机构
    filterForm.ownerOrgs = [...orgs];
    handleSearch();
  }
};

// 处理筛选条件的机构选择取消
const handleFilterOrgCancel = () => {
  filterForm.showOrgSelector = false;
};

// 处理表单中的机构选择
const handleFormOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    // 单选模式，只取第一个机构
    const selectedOrg = orgs[0];
    form.ownerOrgId = selectedOrg.id;
    form.ownerOrgName = selectedOrg.orgName;
    messages.success(`已选择所属单位：${selectedOrg.orgName}`);
  }
};

// 处理表单中的机构选择取消
const handleFormOrgCancel = () => {
  form.showOrgSelector = false;
};

// 清空机构选择
const clearOrgSelection = () => {
  filterForm.ownerOrgs = [];
  handleSearch();
};

// 移除单个机构
const removeOrg = (orgToRemove) => {
  filterForm.ownerOrgs = filterForm.ownerOrgs.filter(
    (org) => org.id !== orgToRemove.id
  );
  handleSearch();
};

// 处理销售顾问选择
const handleSellerSelect = (seller) => {
  if (seller) {
    form.ownerSellerId = seller.id;
    form.ownerSellerName = seller.name;
    messages.success(`已选择销售顾问：${seller.name}`);
  } else {
    form.ownerSellerId = null;
    form.ownerSellerName = "";
  }
};

// 处理客户类型变化
const handleCustomerTypeChange = (value) => {
  // 当客户类型为法人客户时，成交状态默认为未成交
  if (value === "institutional") {
    form.dealStatus = "LEADS"; // 未成交
  } else {
    form.dealStatus = "CUSTOMER"; // 已成交
  }
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  // 页码变化由分页组件的 onChange 事件处理
};

// 处理导入成功
const handleImportSuccess = async (fileInfo) => {
  try {
    messages.success(`文件上传成功: ${fileInfo.fileName}`);
    console.log("文件上传路径:", fileInfo.fileKey);

    // 调用客户导入接口
    const response = await customerApi.importCustomers(fileInfo.fileKey);

    if (response.code === 200) {
      messages.success("文件解析成功");
      // 刷新数据列表
      refreshData();
    } else {
      messages.error(response.message || "文件解析失败");
    }
  } catch (error) {
    console.error("文件解析失败:", error);
    messages.error("文件解析失败，请检查文件格式是否正确");
  }
};

// 处理导入错误
const handleImportError = (errorMsg) => {
  console.error(`导入失败: ${errorMsg}`);
};

// ===== 业务机构成员选择器相关方法 =====

// 显示单选模式的成员选择器
const showMemberSelectorSingle = () => {
  memberSelectorMode.value = "single";
  memberSelectorTitle.value = "选择业务机构成员（单选）";
  memberSelectorInitialOrgId.value = form.ownerOrgId; // 使用当前选中的机构ID
  memberSelectorVisible.value = true;
};

// 显示多选模式的成员选择器
const showMemberSelectorMultiple = () => {
  memberSelectorMode.value = "multiple";
  memberSelectorTitle.value = "选择业务机构成员（多选）";
  memberSelectorInitialOrgId.value = form.ownerOrgId; // 使用当前选中的机构ID
  memberSelectorVisible.value = true;
};

// 处理成员选择
const handleMemberSelected = (selectedData) => {
  if (memberSelectorMode.value === "single") {
    // 单选模式：替换现有选择
    form.selectedMembers = selectedData ? [selectedData] : [];
    messages.success(`已选择成员：${selectedData?.name || "未知"}`);
  } else {
    // 多选模式：设置为选择的数组
    form.selectedMembers = selectedData || [];
    messages.success(`已选择 ${selectedData?.length || 0} 个成员`);
  }

  console.log("选择的成员数据:", selectedData);
};

// 处理成员选择取消
const handleMemberSelectCancel = () => {
  messages.info("已取消选择成员");
};

// 清空选中的成员
const clearSelectedMembers = () => {
  form.selectedMembers = [];
  messages.success("已清空选中的成员");
};

// 从选择中移除单个成员
const removeMemberFromSelection = (memberToRemove) => {
  form.selectedMembers = form.selectedMembers.filter(
    (member) => member.id !== memberToRemove.id
  );
  messages.success(`已移除成员：${memberToRemove.name}`);
};
</script>

<style scoped>
.customer-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-card {
  margin-bottom: 0;
}

.toolbar {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

.n-data-table {
  flex: 1;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 表单样式 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  padding-bottom: 4px;
}

:deep(.n-form-item) {
  margin-bottom: 8px;
}

:deep(.n-input) {
  border-radius: 4px;
}

:deep(.n-select) {
  width: 100%;
}

/* 确保选择器组件在弹窗缩小时正确显示 */
:deep(.department-selector),
:deep(.member-selector) {
  width: 100% !important;
  min-width: 100% !important;
}

:deep(.n-tag) {
  margin: 0;
}

/* 成员选择器测试区域样式 */
.selected-members-display {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(24, 160, 88, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(24, 160, 88, 0.2);
}

.member-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 6px;
}
</style>