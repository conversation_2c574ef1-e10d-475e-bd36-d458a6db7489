import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import MainPage from '@/views/MainPage.vue'
import LoginPage from '@/views/system/LoginPage.vue'
import DashboardPage from '@/views/system/HomePage.vue'
import NotFoundPage from '@/views/system/NotFoundPage.vue'
import AppIndex from '@/views/app/AppIndex.vue'
import ReceivableSelectorExample from '@/views/examples/ReceivableSelectorExample.vue'
import DepositOrderPage from '@/views/inventory/DepositOrderPage.vue'
import BizOrgSelectorTest from '@/views/test/BizOrgSelectorTest.vue'
import QueryPageTest from '@/views/test/QueryPageTest.vue'
import TestIndex from '@/views/test/TestIndex.vue'
import GiftStockPageNew from '@/views/inventory/GiftStockPageNew.vue'
import RouterTest from '@/views/test/RouterTest.vue'

const staticRoutes = [
  {
    path: '/index.html',
    redirect: '/'
  },
  {
    path: '/login',
    name: 'Login',
    component: LoginPage
  },
  {
    path: '/',
    name: 'Main',
    component: MainPage,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: DashboardPage,
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/app/index/:menuId',
    name: 'AppIndex',
    component: AppIndex,
    meta: { requiresAuth: false }
  },
  {
    path: '/examples/receivable-selector',
    name: 'ReceivableSelectorExample',
    component: ReceivableSelectorExample,
    meta: { requiresAuth: false }
  },
  {
    path: '/inventory/deposit-order',
    name: 'DepositOrder',
    component: DepositOrderPage,
    meta: { requiresAuth: true, title: '定金订单管理' }
  },
  {
    path: '/test',
    name: 'TestIndex',
    component: TestIndex,
    meta: { requiresAuth: false, title: '测试页面导航' }
  },
  {
    path: '/test/biz-org-selector',
    name: 'BizOrgSelectorTest',
    component: BizOrgSelectorTest,
    meta: { requiresAuth: false, title: '业务机构选择器测试' }
  },
  {
    path: '/test/query-page',
    name: 'QueryPageTest',
    component: QueryPageTest,
    meta: { requiresAuth: false, title: 'QueryPage功能测试' }
  },
  {
    path: '/test/gift-stock',
    name: 'GiftStockTest',
    component: GiftStockPageNew,
    meta: { requiresAuth: false, title: '赠品库存测试' }
  },
  {
    path: '/test/router',
    name: 'RouterTest',
    component: RouterTest,
    meta: { requiresAuth: false, title: '路由模式测试' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFoundPage
  }
]

// 根据环境选择路由模式
// 开发环境使用History模式，生产环境使用Hash模式（避免CDN 404问题）
const router = createRouter({
  history: import.meta.env.MODE === 'production'
    ? createWebHashHistory()
    : createWebHistory(),
  routes: staticRoutes
})

export function resetRouter() {
  const newRouter = createRouter({
    history: import.meta.env.MODE === 'production'
      ? createWebHashHistory()
      : createWebHistory(),
    routes: staticRoutes
  })
  router.matcher = newRouter.matcher
}

export function addDynamicRoutes(menus) {
  if (!menus || !Array.isArray(menus)) {
    return;
  }

  // 使用import.meta.glob预加载所有可能的视图组件
  // 这会在构建时创建一个路径映射，但组件仍然是按需加载的
  // 同时使用多种路径格式来确保能找到组件
  const viewModules = {
    ...import.meta.glob('@/views/**/*.vue'),
    ...import.meta.glob('/src/views/**/*.vue'),
    ...import.meta.glob('../views/**/*.vue')
  };

  // 手动排除已经静态导入的NotFoundPage组件
  // 删除NotFoundPage的动态导入引用
  const notFoundPaths = [
    '@/views/system/NotFoundPage.vue',
    '/src/views/system/NotFoundPage.vue',
    '../views/system/NotFoundPage.vue'
  ];

  notFoundPaths.forEach(path => {
    if (viewModules[path]) {
      delete viewModules[path];
    }
  });
  try {
    const addRoutes = (menuItems, parentPath = '') => {
      menuItems.forEach(menu => {
        if (menu.viewPath) {
          // 安全地处理menuPath
          const menuPath = menu.menuPath || '';
          const fullPath = parentPath + (menuPath.startsWith('/') ? menuPath : '/' + menuPath);
          // 构建可能的组件路径
          const possiblePaths = [
            `@/views/${menu.viewPath}.vue`,
            `/src/views/${menu.viewPath}.vue`,
            `../views/${menu.viewPath}.vue`
          ];
          // 检查组件是否存在于预加载的映射中
          const componentPath = possiblePaths.find(path => viewModules[path]);

          if (componentPath) {
            const route = {
              path: fullPath,
              name: menu.menuLabel,
              component: viewModules[componentPath], // 使用预加载的懒加载函数
              meta: { requiresAuth: true, title: menu.menuLabel }
            };

            if (!router.hasRoute(route.name)) {
              router.addRoute('Main', route);
            }
          }
        }

        if (menu.subMenus && menu.subMenus.length) {
          const newParentPath = parentPath + (menu.menuPath ? (menu.menuPath.startsWith('/') ? menu.menuPath : '/' + menu.menuPath) : '');
          addRoutes(menu.subMenus, newParentPath);
        }
      });
    };

    addRoutes(menus);
  } catch (error) {
  }
}

router.beforeEach(async (to, _from, next) => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (isAuthenticated && to.path === '/login') {
    next('/')
  } else {
    const storedMenus = localStorage.getItem('menus')
    if (isAuthenticated && storedMenus) {
      try {
        const parsedMenus = JSON.parse(storedMenus)
        addDynamicRoutes(parsedMenus)

        // 使用router.resolve来检查路由是否匹配
        const routeLocation = router.resolve(to)
        if (routeLocation.matched.length === 0) {
          // 如果路由不匹配，可能是动态路由还没有完全加载，重新导航
          next({ ...to, replace: true })
        } else {
          next()
        }
      } catch (error) {
        localStorage.removeItem('menus')
        next('/login')
      }
    } else {
      next()
    }
  }
})

// 导出菜单工具函数，供其他组件使用
export { findMenuByPath } from './menuUtils'
export { loadDynamicRoutes } from './menuUtils'

export default router

router.onError((error) => {
  console.error('🚨 路由错误:', error);
});

// 添加404路由处理
router.beforeResolve((to) => {
  if (to.matched.length === 0) {
    console.warn('⚠️ 404错误: 未找到路由', {
      path: to.path,
      fullPath: to.fullPath,
      params: to.params,
      query: to.query
    });
  }
});